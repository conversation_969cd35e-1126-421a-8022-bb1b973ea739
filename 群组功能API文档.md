# 群组功能API文档

## 概述
本文档描述了SULIAO即时通讯软件的群组功能API接口。所有的群组功能都通过JSON格式的消息进行通信，消息以"END"作为结束标识符。

## 数据库表结构

### Groups表 - 群组基本信息
- `group_id`: 群ID（自增主键）
- `group_name`: 群名称
- `creator_id`: 创建者QQ号
- `description`: 群简介
- `avatar`: 群头像（Base64编码）
- `create_time`: 创建时间
- `max_members`: 群最大成员数（默认500）

### GroupMembers表 - 群成员关系
- `id`: 自增主键
- `group_id`: 所属群ID
- `member_id`: 成员QQ号
- `join_time`: 加入时间
- `role`: 成员角色（owner/admin/member）
- `nickname_in_group`: 群内昵称
- `last_read_msg_id`: 最后已读消息ID

### GroupMessages表 - 群聊消息
- `message_id`: 消息唯一ID
- `group_id`: 所属群ID
- `sender_id`: 发送者QQ号
- `content`: 消息内容
- `filename`: 文件名
- `file_size`: 文件大小
- `message_type`: 消息类型（text/image/video/file/audio）
- `status`: 消息状态（sent/received/read）
- `timestamp`: 发送时间

## API接口

### 1. 创建群组
**请求格式：**
```json
{
    "tag": "creategroup",
    "group_name": "群名称",
    "creator_id": "创建者QQ号",
    "description": "群简介",
    "avatar": "群头像Base64",
    "max_members": 500
}
```

**响应格式：**
```json
{
    "tag": "creategroup",
    "answer": "succeed/fail",
    "group_id": 群ID,
    "group_name": "群名称",
    "message": "错误信息（失败时）"
}
```

### 2. 加入群组
**请求格式：**
```json
{
    "tag": "joingroup",
    "group_id": 群ID,
    "member_id": "用户QQ号"
}
```

**响应格式：**
```json
{
    "tag": "joingroup",
    "answer": "succeed/fail",
    "group_id": 群ID,
    "group_name": "群名称",
    "message": "错误信息（失败时）"
}
```

### 3. 退出群组
**请求格式：**
```json
{
    "tag": "leavegroup",
    "group_id": 群ID,
    "member_id": "用户QQ号"
}
```

**响应格式：**
```json
{
    "tag": "leavegroup",
    "answer": "succeed/fail",
    "group_id": 群ID,
    "message": "提示信息"
}
```

### 4. 发送群聊消息
**请求格式：**
```json
{
    "tag": "groupmessages",
    "group_id": 群ID,
    "sender_id": "发送者QQ号",
    "content": "消息内容",
    "message_type": "text/image/file/audio/video",
    "filename": "文件名（可选）",
    "file_size": 文件大小（可选）
}
```

**响应格式：**
```json
{
    "tag": "groupmessages",
    "answer": "succeed/fail",
    "message_id": 消息ID,
    "message": "错误信息（失败时）"
}
```

### 5. 获取用户群组列表
**请求格式：**
```json
{
    "tag": "getgrouplist",
    "user_id": "用户QQ号"
}
```

**响应格式：**
```json
{
    "tag": "getgrouplist",
    "answer": "succeed/fail",
    "groups": [
        {
            "group_id": 群ID,
            "group_name": "群名称",
            "description": "群简介",
            "avatar": "群头像",
            "create_time": "创建时间",
            "max_members": 最大成员数,
            "member_count": 当前成员数,
            "my_role": "我的角色",
            "join_time": "加入时间",
            "nickname_in_group": "群内昵称"
        }
    ]
}
```

### 6. 获取群成员列表
**请求格式：**
```json
{
    "tag": "getgroupmembers",
    "group_id": 群ID,
    "requester_id": "请求者QQ号"
}
```

**响应格式：**
```json
{
    "tag": "getgroupmembers",
    "answer": "succeed/fail",
    "group_id": 群ID,
    "members": [
        {
            "member_id": "成员QQ号",
            "role": "角色",
            "join_time": "加入时间",
            "nickname_in_group": "群内昵称",
            "nickname": "用户昵称",
            "signature": "个性签名",
            "gender": "性别"
        }
    ]
}
```

### 7. 更新群组信息
**请求格式：**
```json
{
    "tag": "updategroupinfo",
    "group_id": 群ID,
    "operator_id": "操作者QQ号",
    "group_name": "新群名称（可选）",
    "description": "新群简介（可选）",
    "avatar": "新群头像（可选）",
    "max_members": 新最大成员数（可选，仅群主）
}
```

### 8. 踢出群成员
**请求格式：**
```json
{
    "tag": "kickgroupmember",
    "group_id": 群ID,
    "operator_id": "操作者QQ号",
    "target_member_id": "目标成员QQ号"
}
```

### 9. 设置群管理员
**请求格式：**
```json
{
    "tag": "setgroupadmin",
    "group_id": 群ID,
    "operator_id": "操作者QQ号（必须是群主）",
    "target_member_id": "目标成员QQ号",
    "new_role": "admin/member"
}
```

## 群组通知消息

### 成员加入通知
```json
{
    "tag": "groupnotification",
    "type": "member_joined",
    "group_id": 群ID,
    "member_id": "新成员QQ号",
    "message": "XXX 加入了群聊"
}
```

### 成员退出通知
```json
{
    "tag": "groupnotification",
    "type": "member_left",
    "group_id": 群ID,
    "member_id": "退出成员QQ号",
    "message": "XXX 退出了群聊"
}
```

### 群聊消息转发
```json
{
    "tag": "yourgroupmessages",
    "group_id": 群ID,
    "sender_id": "发送者QQ号",
    "content": "消息内容",
    "message_type": "消息类型",
    "message_id": 消息ID,
    "timestamp": "发送时间"
}
```

## 权限说明
- **群主（owner）**：可以进行所有操作，包括解散群聊、设置管理员、踢出任何成员
- **管理员（admin）**：可以踢出普通成员、修改群信息（除最大成员数）
- **普通成员（member）**：只能发送消息、查看群信息、主动退出群聊

## 注意事项
1. 所有消息都以"END"结尾
2. 群主退出群聊时，如果群内还有其他成员，需要先转让群主或解散群聊
3. 群成员数量不能超过设定的最大成员数
4. 只有群成员才能发送群聊消息和查看群成员列表
