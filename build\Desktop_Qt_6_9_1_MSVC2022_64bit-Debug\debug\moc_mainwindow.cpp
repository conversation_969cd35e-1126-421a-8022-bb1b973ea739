/****************************************************************************
** Meta object code from reading C++ file 'mainwindow.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../mainwindow.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'mainwindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN10MainWindowE_t {};
} // unnamed namespace

template <> constexpr inline auto MainWindow::qt_create_metaobjectdata<qt_meta_tag_ZN10MainWindowE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "MainWindow",
        "deleteDone",
        "",
        "serachResult",
        "json",
        "changeResult",
        "changePasswordAnswer1",
        "changePasswordAnswer2",
        "logoutAnswer",
        "saveDone",
        "status",
        "returnToLogin",
        "on_but_maxwindow_clicked",
        "on_but_minwindow_clicked",
        "on_but_deletewindow_clicked",
        "on_but_minwindow2_clicked",
        "on_but_maxwindow2_clicked",
        "on_but_deletewindow2_clicked",
        "on_but_chat_clicked",
        "on_but_friends_clicked",
        "on_but_groups_clicked",
        "on_edit_input_textChanged",
        "on_pages_currentChanged",
        "arg1",
        "on_but_addfriends_clicked",
        "on_but_add0_clicked",
        "goSerachFriends",
        "account",
        "clearEditShow2",
        "updateEditShow2New",
        "updateEditShow2Normal",
        "QModelIndex",
        "index",
        "on_line_serach2_textChanged",
        "on_line_serach_textChanged",
        "deleteFriend",
        "goAddFriends",
        "friendAccount",
        "changeInfo",
        "listtalkChoice",
        "sendMessage",
        "MyAccountInfo",
        "info",
        "changePassword1",
        "changePassword2",
        "goLogout",
        "rejectAddFriends",
        "sender",
        "acceptAddFriends",
        "on_but_send_clicked",
        "on_but_tool1_clicked",
        "on_but_tool2_clicked",
        "sendDoc",
        "jsonData",
        "filename",
        "timestamp",
        "receiver",
        "handleSaveDone",
        "onTalkItemCurrentChanged",
        "onFriendItemCurrentChanged",
        "mousePressEvent",
        "QMouseEvent*",
        "event",
        "mouseMoveEvent",
        "mouseReleaseEvent",
        "onReadyRead"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'deleteDone'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'serachResult'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(3, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Signal 'changeResult'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Signal 'changePasswordAnswer1'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(6, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Signal 'changePasswordAnswer2'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Signal 'logoutAnswer'
        QtMocHelpers::SignalData<void(const QJsonObject &)>(8, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Signal 'saveDone'
        QtMocHelpers::SignalData<void(QString)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 10 },
        }}),
        // Signal 'returnToLogin'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'on_but_maxwindow_clicked'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_minwindow_clicked'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_deletewindow_clicked'
        QtMocHelpers::SlotData<void()>(14, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_minwindow2_clicked'
        QtMocHelpers::SlotData<void()>(15, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_maxwindow2_clicked'
        QtMocHelpers::SlotData<void()>(16, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_deletewindow2_clicked'
        QtMocHelpers::SlotData<void()>(17, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_chat_clicked'
        QtMocHelpers::SlotData<void()>(18, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_friends_clicked'
        QtMocHelpers::SlotData<void()>(19, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_groups_clicked'
        QtMocHelpers::SlotData<void()>(20, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_edit_input_textChanged'
        QtMocHelpers::SlotData<void()>(21, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_pages_currentChanged'
        QtMocHelpers::SlotData<void(int)>(22, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 23 },
        }}),
        // Slot 'on_but_addfriends_clicked'
        QtMocHelpers::SlotData<void()>(24, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_add0_clicked'
        QtMocHelpers::SlotData<void()>(25, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'goSerachFriends'
        QtMocHelpers::SlotData<void(const QString &)>(26, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 27 },
        }}),
        // Slot 'clearEditShow2'
        QtMocHelpers::SlotData<void()>(28, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'updateEditShow2New'
        QtMocHelpers::SlotData<void()>(29, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'updateEditShow2Normal'
        QtMocHelpers::SlotData<void(const QModelIndex &)>(30, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 31, 32 },
        }}),
        // Slot 'on_line_serach2_textChanged'
        QtMocHelpers::SlotData<void(const QString &)>(33, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 23 },
        }}),
        // Slot 'on_line_serach_textChanged'
        QtMocHelpers::SlotData<void(const QString &)>(34, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 23 },
        }}),
        // Slot 'deleteFriend'
        QtMocHelpers::SlotData<void()>(35, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'goAddFriends'
        QtMocHelpers::SlotData<void(const QString &)>(36, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 37 },
        }}),
        // Slot 'changeInfo'
        QtMocHelpers::SlotData<void()>(38, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'listtalkChoice'
        QtMocHelpers::SlotData<void(const QJsonObject &)>(39, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Slot 'sendMessage'
        QtMocHelpers::SlotData<void(const MyAccountInfo &)>(40, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 41, 42 },
        }}),
        // Slot 'changePassword1'
        QtMocHelpers::SlotData<void(const QJsonObject &)>(43, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Slot 'changePassword2'
        QtMocHelpers::SlotData<void(const QJsonObject &)>(44, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Slot 'goLogout'
        QtMocHelpers::SlotData<void(const QJsonObject &)>(45, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QJsonObject, 4 },
        }}),
        // Slot 'rejectAddFriends'
        QtMocHelpers::SlotData<void(const QString &, const QString &)>(46, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 27 }, { QMetaType::QString, 47 },
        }}),
        // Slot 'acceptAddFriends'
        QtMocHelpers::SlotData<void(const QString &, const QString &)>(48, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 27 }, { QMetaType::QString, 47 },
        }}),
        // Slot 'on_but_send_clicked'
        QtMocHelpers::SlotData<void()>(49, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_tool1_clicked'
        QtMocHelpers::SlotData<void()>(50, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'on_but_tool2_clicked'
        QtMocHelpers::SlotData<void()>(51, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'sendDoc'
        QtMocHelpers::SlotData<void(const QByteArray &, const QString &, const QString &, const QString &)>(52, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QByteArray, 53 }, { QMetaType::QString, 54 }, { QMetaType::QString, 55 }, { QMetaType::QString, 56 },
        }}),
        // Slot 'handleSaveDone'
        QtMocHelpers::SlotData<void(const QString &)>(57, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 10 },
        }}),
        // Slot 'onTalkItemCurrentChanged'
        QtMocHelpers::SlotData<void()>(58, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onFriendItemCurrentChanged'
        QtMocHelpers::SlotData<void()>(59, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'mousePressEvent'
        QtMocHelpers::SlotData<void(QMouseEvent *)>(60, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 61, 62 },
        }}),
        // Slot 'mouseMoveEvent'
        QtMocHelpers::SlotData<void(QMouseEvent *)>(63, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 61, 62 },
        }}),
        // Slot 'mouseReleaseEvent'
        QtMocHelpers::SlotData<void(QMouseEvent *)>(64, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { 0x80000000 | 61, 62 },
        }}),
        // Slot 'onReadyRead'
        QtMocHelpers::SlotData<void()>(65, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MainWindow, qt_meta_tag_ZN10MainWindowE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN10MainWindowE_t>.metaTypes,
    nullptr
} };

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MainWindow *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->deleteDone(); break;
        case 1: _t->serachResult((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 2: _t->changeResult((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 3: _t->changePasswordAnswer1((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 4: _t->changePasswordAnswer2((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 5: _t->logoutAnswer((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 6: _t->saveDone((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->returnToLogin(); break;
        case 8: _t->on_but_maxwindow_clicked(); break;
        case 9: _t->on_but_minwindow_clicked(); break;
        case 10: _t->on_but_deletewindow_clicked(); break;
        case 11: _t->on_but_minwindow2_clicked(); break;
        case 12: _t->on_but_maxwindow2_clicked(); break;
        case 13: _t->on_but_deletewindow2_clicked(); break;
        case 14: _t->on_but_chat_clicked(); break;
        case 15: _t->on_but_friends_clicked(); break;
        case 16: _t->on_but_groups_clicked(); break;
        case 17: _t->on_edit_input_textChanged(); break;
        case 18: _t->on_pages_currentChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 19: _t->on_but_addfriends_clicked(); break;
        case 20: _t->on_but_add0_clicked(); break;
        case 21: _t->goSerachFriends((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 22: _t->clearEditShow2(); break;
        case 23: _t->updateEditShow2New(); break;
        case 24: _t->updateEditShow2Normal((*reinterpret_cast< std::add_pointer_t<QModelIndex>>(_a[1]))); break;
        case 25: _t->on_line_serach2_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 26: _t->on_line_serach_textChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 27: _t->deleteFriend(); break;
        case 28: _t->goAddFriends((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 29: _t->changeInfo(); break;
        case 30: _t->listtalkChoice((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 31: _t->sendMessage((*reinterpret_cast< std::add_pointer_t<MyAccountInfo>>(_a[1]))); break;
        case 32: _t->changePassword1((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 33: _t->changePassword2((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 34: _t->goLogout((*reinterpret_cast< std::add_pointer_t<QJsonObject>>(_a[1]))); break;
        case 35: _t->rejectAddFriends((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 36: _t->acceptAddFriends((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 37: _t->on_but_send_clicked(); break;
        case 38: _t->on_but_tool1_clicked(); break;
        case 39: _t->on_but_tool2_clicked(); break;
        case 40: _t->sendDoc((*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[4]))); break;
        case 41: _t->handleSaveDone((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 42: _t->onTalkItemCurrentChanged(); break;
        case 43: _t->onFriendItemCurrentChanged(); break;
        case 44: _t->mousePressEvent((*reinterpret_cast< std::add_pointer_t<QMouseEvent*>>(_a[1]))); break;
        case 45: _t->mouseMoveEvent((*reinterpret_cast< std::add_pointer_t<QMouseEvent*>>(_a[1]))); break;
        case 46: _t->mouseReleaseEvent((*reinterpret_cast< std::add_pointer_t<QMouseEvent*>>(_a[1]))); break;
        case 47: _t->onReadyRead(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)()>(_a, &MainWindow::deleteDone, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)(const QJsonObject & )>(_a, &MainWindow::serachResult, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)(const QJsonObject & )>(_a, &MainWindow::changeResult, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)(const QJsonObject & )>(_a, &MainWindow::changePasswordAnswer1, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)(const QJsonObject & )>(_a, &MainWindow::changePasswordAnswer2, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)(const QJsonObject & )>(_a, &MainWindow::logoutAnswer, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)(QString )>(_a, &MainWindow::saveDone, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (MainWindow::*)()>(_a, &MainWindow::returnToLogin, 7))
            return;
    }
}

const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN10MainWindowE_t>.strings))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 48)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 48;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 48)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 48;
    }
    return _id;
}

// SIGNAL 0
void MainWindow::deleteDone()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void MainWindow::serachResult(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void MainWindow::changeResult(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void MainWindow::changePasswordAnswer1(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void MainWindow::changePasswordAnswer2(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void MainWindow::logoutAnswer(const QJsonObject & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}

// SIGNAL 6
void MainWindow::saveDone(QString _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 6, nullptr, _t1);
}

// SIGNAL 7
void MainWindow::returnToLogin()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
QT_WARNING_POP
